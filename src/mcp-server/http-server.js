#!/usr/bin/env node

import express from 'express';
import cors from 'cors';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
} from '@modelcontextprotocol/sdk/types.js';
import { HotelSearchService } from './hotel-search-service.js';
import dotenv from 'dotenv';

dotenv.config();

class HotelSearchHTTPServer {
  constructor(port = 3001) {
    this.port = port;
    this.app = express();
    this.hotelSearchService = new HotelSearchService();
    
    // Setup middleware
    this.app.use(cors());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.text());
    
    this.setupRoutes();
  }

  setupRoutes() {
    // Health check
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'healthy',
        service: 'hotel-search-mcp-server',
        timestamp: new Date().toISOString(),
      });
    });

    // List available tools
    this.app.get('/tools', async (req, res) => {
      try {
        const tools = [
          {
            name: 'search_hotels',
            description: 'Search for hotels based on destination, dates, and guest count',
            inputSchema: {
              type: 'object',
              properties: {
                destination: {
                  type: 'string',
                  description: 'Destination city or location',
                },
                checkin_date: {
                  type: 'string',
                  description: 'Check-in date in YYYY-MM-DD format',
                },
                checkout_date: {
                  type: 'string',
                  description: 'Check-out date in YYYY-MM-DD format',
                },
                guests: {
                  type: 'integer',
                  description: 'Number of guests',
                  minimum: 1,
                  maximum: 10,
                },
                rooms: {
                  type: 'integer',
                  description: 'Number of rooms (optional, defaults to 1)',
                  minimum: 1,
                  maximum: 5,
                },
              },
              required: ['destination', 'checkin_date', 'checkout_date', 'guests'],
            },
          },
          {
            name: 'validate_booking_parameters',
            description: 'Validate if all required booking parameters are collected',
            inputSchema: {
              type: 'object',
              properties: {
                destination: { type: 'string' },
                checkin_date: { type: 'string' },
                checkout_date: { type: 'string' },
                guests: { type: 'integer' },
              },
              required: ['destination', 'checkin_date', 'checkout_date', 'guests'],
            },
          },
        ];

        res.json({
          success: true,
          tools,
        });
      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message,
        });
      }
    });

    // Execute tool calls
    this.app.post('/tools/:toolName', async (req, res) => {
      const { toolName } = req.params;
      const parameters = req.body;

      try {
        let result;
        
        switch (toolName) {
          case 'search_hotels':
            result = await this.handleHotelSearch(parameters);
            break;
          
          case 'validate_booking_parameters':
            result = await this.handleParameterValidation(parameters);
            break;
          
          default:
            return res.status(400).json({
              success: false,
              error: `Unknown tool: ${toolName}`,
            });
        }

        res.json({
          success: true,
          data: result,
        });

      } catch (error) {
        console.error(`Error executing tool ${toolName}:`, error);
        res.status(500).json({
          success: false,
          error: error.message,
        });
      }
    });

    // Streaming endpoint for real-time updates
    this.app.post('/tools/:toolName/stream', async (req, res) => {
      const { toolName } = req.params;
      const parameters = req.body;

      // Set up Server-Sent Events
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      });

      const sendEvent = (event, data) => {
        res.write(`event: ${event}\n`);
        res.write(`data: ${JSON.stringify(data)}\n\n`);
      };

      try {
        sendEvent('start', { toolName, parameters });

        let result;
        
        switch (toolName) {
          case 'search_hotels':
            sendEvent('progress', { message: 'Validating search parameters...' });
            await new Promise(resolve => setTimeout(resolve, 100));
            
            sendEvent('progress', { message: 'Searching hotels...' });
            result = await this.handleHotelSearch(parameters);
            
            sendEvent('progress', { message: 'Processing results...' });
            await new Promise(resolve => setTimeout(resolve, 50));
            break;
          
          case 'validate_booking_parameters':
            sendEvent('progress', { message: 'Validating parameters...' });
            result = await this.handleParameterValidation(parameters);
            break;
          
          default:
            throw new Error(`Unknown tool: ${toolName}`);
        }

        sendEvent('result', { success: true, data: result });
        sendEvent('complete', { message: 'Tool execution completed' });

      } catch (error) {
        console.error(`Error executing streaming tool ${toolName}:`, error);
        sendEvent('error', { success: false, error: error.message });
      } finally {
        res.end();
      }
    });

    // Batch tool execution
    this.app.post('/tools/batch', async (req, res) => {
      const { tools } = req.body;

      if (!Array.isArray(tools)) {
        return res.status(400).json({
          success: false,
          error: 'Expected array of tools',
        });
      }

      try {
        const results = await Promise.allSettled(
          tools.map(async ({ name, parameters }) => {
            switch (name) {
              case 'search_hotels':
                return await this.handleHotelSearch(parameters);
              case 'validate_booking_parameters':
                return await this.handleParameterValidation(parameters);
              default:
                throw new Error(`Unknown tool: ${name}`);
            }
          })
        );

        const processedResults = results.map((result, index) => ({
          toolName: tools[index].name,
          success: result.status === 'fulfilled',
          data: result.status === 'fulfilled' ? result.value : null,
          error: result.status === 'rejected' ? result.reason.message : null,
        }));

        res.json({
          success: true,
          results: processedResults,
        });

      } catch (error) {
        res.status(500).json({
          success: false,
          error: error.message,
        });
      }
    });
  }

  async handleHotelSearch(args) {
    const { destination, checkin_date, checkout_date, guests, rooms = 1 } = args;
    
    // Validate dates
    const checkinDate = new Date(checkin_date);
    const checkoutDate = new Date(checkout_date);
    const today = new Date();
    
    if (checkinDate < today) {
      throw new Error('Check-in date cannot be in the past');
    }
    
    if (checkoutDate <= checkinDate) {
      throw new Error('Check-out date must be after check-in date');
    }

    const searchResults = await this.hotelSearchService.searchHotels({
      destination,
      checkinDate: checkin_date,
      checkoutDate: checkout_date,
      guests,
      rooms,
    });

    return {
      success: true,
      search_parameters: {
        destination,
        checkin_date,
        checkout_date,
        guests,
        rooms,
      },
      results: searchResults,
      total_results: searchResults.length,
    };
  }

  async handleParameterValidation(args) {
    const { destination, checkin_date, checkout_date, guests } = args;
    
    const missing = [];
    const issues = [];

    if (!destination || destination.trim() === '') {
      missing.push('destination');
    }
    
    if (!checkin_date) {
      missing.push('checkin_date');
    } else {
      const checkinDate = new Date(checkin_date);
      if (isNaN(checkinDate.getTime())) {
        issues.push('Invalid check-in date format');
      } else if (checkinDate < new Date()) {
        issues.push('Check-in date cannot be in the past');
      }
    }
    
    if (!checkout_date) {
      missing.push('checkout_date');
    } else {
      const checkoutDate = new Date(checkout_date);
      if (isNaN(checkoutDate.getTime())) {
        issues.push('Invalid check-out date format');
      }
    }
    
    if (!guests || guests < 1) {
      missing.push('guests');
    }

    if (checkin_date && checkout_date) {
      const checkinDate = new Date(checkin_date);
      const checkoutDate = new Date(checkout_date);
      if (checkoutDate <= checkinDate) {
        issues.push('Check-out date must be after check-in date');
      }
    }

    const isValid = missing.length === 0 && issues.length === 0;

    return {
      valid: isValid,
      missing_parameters: missing,
      validation_issues: issues,
      parameters: { destination, checkin_date, checkout_date, guests },
    };
  }

  start() {
    return new Promise((resolve) => {
      this.server = this.app.listen(this.port, () => {
        console.log(`🏨 Hotel Search MCP HTTP Server running on port ${this.port}`);
        console.log(`📋 Available endpoints:`);
        console.log(`   GET  /health - Health check`);
        console.log(`   GET  /tools - List available tools`);
        console.log(`   POST /tools/:toolName - Execute tool`);
        console.log(`   POST /tools/:toolName/stream - Execute tool with streaming`);
        console.log(`   POST /tools/batch - Execute multiple tools`);
        resolve();
      });
    });
  }

  stop() {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(resolve);
      } else {
        resolve();
      }
    });
  }
}

// Start the server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const port = process.env.MCP_SERVER_PORT || 3001;
  const server = new HotelSearchHTTPServer(port);
  
  server.start().catch(console.error);
  
  // Graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Shutting down MCP HTTP server...');
    await server.stop();
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 Shutting down MCP HTTP server...');
    await server.stop();
    process.exit(0);
  });
}

export { HotelSearchHTTPServer };
