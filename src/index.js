import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';
import { ConversationManager } from './conversation/conversation-manager.js';

// Load environment variables
dotenv.config();

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const port = process.env.PORT || 3000;

// Initialize conversation manager
const conversationManager = new ConversationManager();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Health check endpoint
app.get('/health', (req, res) => {
  const poolStats = conversationManager.mcpPool.getStats();
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: 'llama-hotel-search',
    mcpPool: poolStats
  });
});

// MCP Pool status endpoint
app.get('/api/mcp-pool/status', (req, res) => {
  try {
    const stats = conversationManager.mcpPool.getStats();
    res.json({
      success: true,
      data: {
        ...stats,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: 'Failed to get MCP pool status',
      message: error.message,
    });
  }
});

// Start a new conversation
app.post('/api/conversation/start', async (req, res) => {
  try {
    const result = await conversationManager.startConversation();
    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error starting conversation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to start conversation',
      message: error.message,
    });
  }
});

// Send a message in a conversation
app.post('/api/conversation/:conversationId/message', async (req, res) => {
  try {
    const { conversationId } = req.params;
    const { message } = req.body;

    if (!message || typeof message !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Message is required and must be a string',
      });
    }

    const result = await conversationManager.processMessage(conversationId, message);
    res.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error processing message:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process message',
      message: error.message,
    });
  }
});

// Get conversation details
app.get('/api/conversation/:conversationId', (req, res) => {
  try {
    const { conversationId } = req.params;
    const conversation = conversationManager.getConversation(conversationId);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found',
      });
    }

    res.json({
      success: true,
      data: {
        id: conversation.id,
        status: conversation.status,
        bookingParameters: conversation.bookingParameters,
        messageCount: conversation.history.length,
        created_at: conversation.created_at,
      },
    });
  } catch (error) {
    console.error('Error getting conversation:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conversation',
      message: error.message,
    });
  }
});

// Get conversation history
app.get('/api/conversation/:conversationId/history', (req, res) => {
  try {
    const { conversationId } = req.params;
    const conversation = conversationManager.getConversation(conversationId);

    if (!conversation) {
      return res.status(404).json({
        success: false,
        error: 'Conversation not found',
      });
    }

    res.json({
      success: true,
      data: {
        conversationId,
        history: conversation.history,
        bookingParameters: conversation.bookingParameters,
        status: conversation.status,
      },
    });
  } catch (error) {
    console.error('Error getting conversation history:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get conversation history',
      message: error.message,
    });
  }
});

// List all conversations (for debugging/admin)
app.get('/api/conversations', (req, res) => {
  try {
    const conversations = conversationManager.getAllConversations();
    const summary = conversations.map(conv => ({
      id: conv.id,
      status: conv.status,
      messageCount: conv.history.length,
      bookingParameters: conv.bookingParameters,
      created_at: conv.created_at,
    }));

    res.json({
      success: true,
      data: {
        total: conversations.length,
        conversations: summary,
      },
    });
  } catch (error) {
    console.error('Error listing conversations:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to list conversations',
      message: error.message,
    });
  }
});

// Serve the main HTML file for all other routes (SPA)
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: process.env.NODE_ENV === 'development' ? error.message : 'Something went wrong',
  });
});

// Start the server
app.listen(port, () => {
  console.log(`🚀 Hotel Search Chatbot server running on port ${port}`);
  console.log(`📱 Open http://localhost:${port} to start chatting`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);

  if (process.env.NODE_ENV === 'development') {
    console.log('📋 Available endpoints:');
    console.log('  GET  /health - Health check');
    console.log('  POST /api/conversation/start - Start new conversation');
    console.log('  POST /api/conversation/:id/message - Send message');
    console.log('  GET  /api/conversation/:id - Get conversation details');
    console.log('  GET  /api/conversation/:id/history - Get conversation history');
    console.log('  GET  /api/conversations - List all conversations');
  }
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down server...');
  process.exit(0);
});
