import { MCPHTTPClient } from './mcp-http-client.js';
import { HotelSearchHTTPServer } from '../mcp-server/http-server.js';

/**
 * Connection pool manager for MCP HTTP clients to handle concurrent requests
 */
export class MCPConnectionPool {
  constructor(options = {}) {
    this.maxConnections = options.maxConnections || 5;
    this.connectionTimeout = options.connectionTimeout || 30000; // 30 seconds
    this.idleTimeout = options.idleTimeout || 300000; // 5 minutes
    this.serverPort = options.serverPort || 3001;
    this.baseUrl = options.baseUrl || `http://localhost:${this.serverPort}`;

    // Start the HTTP server if not already running
    this.server = null;
    this.serverStarted = false;

    this.pool = [];
    this.activeConnections = new Set();
    this.waitingQueue = [];
    this.connectionCount = 0;

    // Cleanup idle connections periodically
    this.cleanupInterval = setInterval(() => {
      this.cleanupIdleConnections();
    }, 60000); // Check every minute
  }

  /**
   * Ensure the MCP HTTP server is running
   */
  async ensureServerRunning() {
    if (this.serverStarted) {
      return;
    }

    try {
      // Check if server is already running
      const testClient = new MCPHTTPClient(this.baseUrl);
      const isHealthy = await testClient.isHealthy();

      if (isHealthy) {
        console.log('MCP HTTP server already running');
        this.serverStarted = true;
        return;
      }
    } catch (error) {
      // Server not running, start it
    }

    try {
      this.server = new HotelSearchHTTPServer(this.serverPort);
      await this.server.start();
      this.serverStarted = true;

      // Wait a moment for server to be fully ready
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('Failed to start MCP HTTP server:', error);
      throw error;
    }
  }

  /**
   * Get a connection from the pool or create a new one
   */
  async getConnection() {
    // Ensure server is running first
    await this.ensureServerRunning();

    return new Promise((resolve, reject) => {
      // Try to get an available connection from the pool
      const availableConnection = this.pool.find(conn => !conn.inUse);

      if (availableConnection) {
        availableConnection.inUse = true;
        availableConnection.lastUsed = Date.now();
        this.activeConnections.add(availableConnection);
        resolve(availableConnection.client);
        return;
      }

      // If we can create a new connection
      if (this.connectionCount < this.maxConnections) {
        this.createConnection()
          .then(connection => {
            connection.inUse = true;
            connection.lastUsed = Date.now();
            this.activeConnections.add(connection);
            resolve(connection.client);
          })
          .catch(reject);
        return;
      }

      // Add to waiting queue
      const timeoutId = setTimeout(() => {
        const index = this.waitingQueue.findIndex(item => item.resolve === resolve);
        if (index !== -1) {
          this.waitingQueue.splice(index, 1);
          reject(new Error('Connection timeout: No available connections'));
        }
      }, this.connectionTimeout);

      this.waitingQueue.push({
        resolve,
        reject,
        timeoutId,
        timestamp: Date.now(),
      });
    });
  }

  /**
   * Release a connection back to the pool
   */
  releaseConnection(client) {
    const connection = this.pool.find(conn => conn.client === client);

    if (connection) {
      connection.inUse = false;
      connection.lastUsed = Date.now();
      this.activeConnections.delete(connection);

      // Process waiting queue
      if (this.waitingQueue.length > 0) {
        const waiting = this.waitingQueue.shift();
        clearTimeout(waiting.timeoutId);

        connection.inUse = true;
        connection.lastUsed = Date.now();
        this.activeConnections.add(connection);
        waiting.resolve(connection.client);
      }
    }
  }

  /**
   * Create a new connection
   */
  async createConnection() {
    try {
      const client = new MCPHTTPClient(this.baseUrl);
      await client.connect();

      const connection = {
        client,
        inUse: false,
        created: Date.now(),
        lastUsed: Date.now(),
        id: `http_conn_${this.connectionCount++}`,
      };

      this.pool.push(connection);
      console.log(`Created new MCP HTTP connection: ${connection.id} (total: ${this.pool.length})`);

      return connection;
    } catch (error) {
      console.error('Failed to create MCP HTTP connection:', error);
      throw error;
    }
  }

  /**
   * Clean up idle connections
   */
  cleanupIdleConnections() {
    const now = Date.now();
    const connectionsToRemove = [];

    for (const connection of this.pool) {
      if (!connection.inUse && (now - connection.lastUsed) > this.idleTimeout) {
        connectionsToRemove.push(connection);
      }
    }

    for (const connection of connectionsToRemove) {
      this.removeConnection(connection);
    }

    if (connectionsToRemove.length > 0) {
      console.log(`Cleaned up ${connectionsToRemove.length} idle MCP connections`);
    }
  }

  /**
   * Remove a connection from the pool
   */
  async removeConnection(connection) {
    try {
      await connection.client.disconnect();
    } catch (error) {
      console.error('Error disconnecting MCP client:', error);
    }

    const index = this.pool.indexOf(connection);
    if (index !== -1) {
      this.pool.splice(index, 1);
    }

    this.activeConnections.delete(connection);
    console.log(`Removed MCP connection: ${connection.id} (remaining: ${this.pool.length})`);
  }

  /**
   * Execute a tool call with automatic connection management
   */
  async executeToolCall(toolName, parameters) {
    let client = null;

    try {
      client = await this.getConnection();
      const result = await client.callTool(toolName, parameters);
      return result;
    } finally {
      if (client) {
        this.releaseConnection(client);
      }
    }
  }

  /**
   * Get pool statistics
   */
  getStats() {
    return {
      totalConnections: this.pool.length,
      activeConnections: this.activeConnections.size,
      availableConnections: this.pool.filter(conn => !conn.inUse).length,
      waitingRequests: this.waitingQueue.length,
      maxConnections: this.maxConnections,
    };
  }

  /**
   * Shutdown the connection pool
   */
  async shutdown() {
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    // Reject all waiting requests
    for (const waiting of this.waitingQueue) {
      clearTimeout(waiting.timeoutId);
      waiting.reject(new Error('Connection pool is shutting down'));
    }
    this.waitingQueue = [];

    // Close all connections
    const closePromises = this.pool.map(connection => this.removeConnection(connection));
    await Promise.allSettled(closePromises);

    this.pool = [];
    this.activeConnections.clear();

    // Stop the HTTP server
    if (this.server) {
      await this.server.stop();
      this.server = null;
      this.serverStarted = false;
    }

    console.log('MCP HTTP connection pool shut down');
  }
}

// Singleton instance
let poolInstance = null;

export function getMCPConnectionPool(options = {}) {
  if (!poolInstance) {
    poolInstance = new MCPConnectionPool(options);
  }
  return poolInstance;
}

// Graceful shutdown
process.on('SIGINT', async () => {
  if (poolInstance) {
    await poolInstance.shutdown();
  }
});

process.on('SIGTERM', async () => {
  if (poolInstance) {
    await poolInstance.shutdown();
  }
});
