import { spawn } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

export class MCPClient {
  constructor() {
    this.client = null;
    this.transport = null;
    this.serverProcess = null;
    this.connected = false;
    this.connecting = false; // Add connection state tracking
    this.connectionPromise = null; // Store connection promise for concurrent requests
  }

  async connect() {
    // If already connected, return immediately
    if (this.connected) {
      return;
    }

    // If currently connecting, wait for the existing connection attempt
    if (this.connecting && this.connectionPromise) {
      return await this.connectionPromise;
    }

    // Set connecting state and create connection promise
    this.connecting = true;
    this.connectionPromise = this._performConnection();

    try {
      await this.connectionPromise;
    } finally {
      this.connecting = false;
      this.connectionPromise = null;
    }
  }

  async _performConnection() {
    try {
      // Spawn the MCP server process
      this.serverProcess = spawn('node', ['src/mcp-server/index.js'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        cwd: process.cwd(),
      });

      // Handle server process errors
      this.serverProcess.on('error', (error) => {
        console.error('MCP server process error:', error);
        this.connected = false;
      });

      this.serverProcess.on('exit', (code, signal) => {
        console.log(`MCP server process exited with code ${code}, signal ${signal}`);
        this.connected = false;
      });

      // Create transport using the spawned process
      this.transport = new StdioClientTransport({
        readable: this.serverProcess.stdout,
        writable: this.serverProcess.stdin,
      });

      // Create and connect the client
      this.client = new Client(
        {
          name: 'hotel-search-client',
          version: '1.0.0',
        },
        {
          capabilities: {},
        }
      );

      await this.client.connect(this.transport);
      this.connected = true;

      console.log('Connected to MCP server');
    } catch (error) {
      console.error('Failed to connect to MCP server:', error);
      this.connected = false;
      throw error;
    }
  }

  async disconnect() {
    if (this.client && this.connected) {
      try {
        await this.client.close();
      } catch (error) {
        console.error('Error closing MCP client:', error);
      }
      this.connected = false;
      console.log('Disconnected from MCP server');
    }

    if (this.serverProcess) {
      try {
        this.serverProcess.kill('SIGTERM');
        // Give it a moment to terminate gracefully
        setTimeout(() => {
          if (this.serverProcess && !this.serverProcess.killed) {
            this.serverProcess.kill('SIGKILL');
          }
        }, 5000);
      } catch (error) {
        console.error('Error killing MCP server process:', error);
      }
      this.serverProcess = null;
    }
  }

  async listTools() {
    await this.ensureConnected();

    try {
      const response = await this.client.request(
        { method: 'tools/list' },
        { method: 'tools/list' }
      );
      return response.tools;
    } catch (error) {
      console.error('Error listing tools:', error);
      // Try to reconnect on error
      this.connected = false;
      throw error;
    }
  }

  async callTool(toolName, parameters) {
    await this.ensureConnected();

    try {
      const response = await this.client.request(
        {
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: parameters,
          },
        },
        {
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: parameters,
          },
        }
      );

      if (response.isError) {
        throw new Error(response.content[0]?.text || 'Tool call failed');
      }

      // Parse the response content
      const content = response.content[0]?.text;
      if (content) {
        try {
          return JSON.parse(content);
        } catch (parseError) {
          // If it's not JSON, return as text
          return { text: content };
        }
      }

      return response;
    } catch (error) {
      console.error(`Error calling tool ${toolName}:`, error);
      // Try to reconnect on error
      this.connected = false;
      throw error;
    }
  }

  async searchHotels(searchParams) {
    return await this.callTool('search_hotels', searchParams);
  }

  async validateBookingParameters(parameters) {
    return await this.callTool('validate_booking_parameters', parameters);
  }

  async ensureConnected() {
    if (!this.connected) {
      await this.connect();
    }
  }

  // Graceful shutdown
  async shutdown() {
    try {
      await this.disconnect();
    } catch (error) {
      console.error('Error during MCP client shutdown:', error);
    }
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('Shutting down MCP client...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down MCP client...');
  process.exit(0);
});
