import axios from 'axios';

export class MCPHTTPClient {
  constructor(baseUrl = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
    this.connected = false;
    this.connecting = false;
    this.connectionPromise = null;
    this.timeout = 30000; // 30 seconds
  }

  async connect() {
    // If already connected, return immediately
    if (this.connected) {
      return;
    }

    // If currently connecting, wait for the existing connection attempt
    if (this.connecting && this.connectionPromise) {
      return await this.connectionPromise;
    }

    // Set connecting state and create connection promise
    this.connecting = true;
    this.connectionPromise = this._performConnection();

    try {
      await this.connectionPromise;
    } finally {
      this.connecting = false;
      this.connectionPromise = null;
    }
  }

  async _performConnection() {
    try {
      // Test connection with health check
      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: 5000,
      });

      if (response.data.status === 'healthy') {
        this.connected = true;
        console.log('Connected to MCP HTTP server');
      } else {
        throw new Error('MCP server is not healthy');
      }
    } catch (error) {
      console.error('Failed to connect to MCP HTTP server:', error.message);
      this.connected = false;
      throw error;
    }
  }

  async disconnect() {
    this.connected = false;
    console.log('Disconnected from MCP HTTP server');
  }

  async listTools() {
    await this.ensureConnected();

    try {
      const response = await axios.get(`${this.baseUrl}/tools`, {
        timeout: this.timeout,
      });

      if (response.data.success) {
        return response.data.tools;
      } else {
        throw new Error(response.data.error || 'Failed to list tools');
      }
    } catch (error) {
      console.error('Error listing tools:', error.message);
      // Try to reconnect on error
      this.connected = false;
      throw error;
    }
  }

  async callTool(toolName, parameters) {
    await this.ensureConnected();

    try {
      const response = await axios.post(
        `${this.baseUrl}/tools/${toolName}`,
        parameters,
        {
          timeout: this.timeout,
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.success) {
        return response.data.data;
      } else {
        throw new Error(response.data.error || 'Tool call failed');
      }
    } catch (error) {
      console.error(`Error calling tool ${toolName}:`, error.message);
      // Try to reconnect on error
      this.connected = false;
      throw error;
    }
  }

  async callToolWithStreaming(toolName, parameters, onProgress = null) {
    // For now, fallback to regular call - streaming can be added later
    if (onProgress) {
      onProgress({ type: 'start', data: { toolName, parameters } });
      onProgress({ type: 'progress', data: { message: 'Processing request...' } });
    }

    const result = await this.callTool(toolName, parameters);

    if (onProgress) {
      onProgress({ type: 'result', data: result });
      onProgress({ type: 'complete', data: { message: 'Request completed' } });
    }

    return result;
  }

  async callToolsBatch(toolCalls) {
    await this.ensureConnected();

    try {
      const response = await axios.post(
        `${this.baseUrl}/tools/batch`,
        { tools: toolCalls },
        {
          timeout: this.timeout * 2, // Longer timeout for batch operations
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (response.data.success) {
        return response.data.results;
      } else {
        throw new Error(response.data.error || 'Batch tool call failed');
      }
    } catch (error) {
      console.error('Error calling tools batch:', error.message);
      // Try to reconnect on error
      this.connected = false;
      throw error;
    }
  }

  async searchHotels(searchParams) {
    return await this.callTool('search_hotels', searchParams);
  }

  async searchHotelsWithStreaming(searchParams, onProgress = null) {
    return await this.callToolWithStreaming('search_hotels', searchParams, onProgress);
  }

  async validateBookingParameters(parameters) {
    return await this.callTool('validate_booking_parameters', parameters);
  }

  async ensureConnected() {
    if (!this.connected) {
      await this.connect();
    }
  }

  // Health check method
  async isHealthy() {
    try {
      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: 5000,
      });
      return response.data.status === 'healthy';
    } catch (error) {
      return false;
    }
  }

  // Get server info
  async getServerInfo() {
    try {
      const response = await axios.get(`${this.baseUrl}/health`, {
        timeout: 5000,
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get server info: ${error.message}`);
    }
  }

  // Graceful shutdown
  async shutdown() {
    try {
      await this.disconnect();
    } catch (error) {
      console.error('Error during MCP HTTP client shutdown:', error);
    }
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('Shutting down MCP HTTP client...');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('Shutting down MCP HTTP client...');
  process.exit(0);
});
