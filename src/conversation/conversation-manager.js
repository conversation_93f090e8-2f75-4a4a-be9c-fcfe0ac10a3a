import { LlamaBedrockClient } from '../bedrock/llama-client.js';
import { getMCPConnectionPool } from './mcp-connection-pool.js';
import { v4 as uuidv4 } from 'uuid';

export class ConversationManager {
  constructor() {
    this.llamaClient = new LlamaBedrockClient();
    this.mcpPool = getMCPConnectionPool({
      maxConnections: 5,
      connectionTimeout: 30000,
      idleTimeout: 300000,
    });
    this.conversations = new Map(); // In production, use a database
    this.availableTools = [
      {
        name: 'search_hotels',
        description: 'Search for hotels based on destination, dates, and guest count',
      },
      {
        name: 'validate_booking_parameters',
        description: 'Validate if all required booking parameters are collected',
      },
    ];
  }

  async startConversation() {
    const conversationId = uuidv4();
    const conversation = {
      id: conversationId,
      history: [],
      bookingParameters: {
        destination: null,
        checkin_date: null,
        checkout_date: null,
        guests: null,
      },
      status: 'collecting_parameters',
      created_at: new Date().toISOString(),
    };

    this.conversations.set(conversationId, conversation);

    const welcomeMessage = {
      role: 'assistant',
      content: "Hello! I'm here to help you find the perfect hotel. To get started, I'll need to know a few details about your trip. Where would you like to stay?",
      timestamp: new Date().toISOString(),
    };

    conversation.history.push(welcomeMessage);

    return {
      conversationId,
      message: welcomeMessage.content,
      status: conversation.status,
    };
  }

  async processMessage(conversationId, userMessage) {
    const conversation = this.conversations.get(conversationId);
    if (!conversation) {
      throw new Error('Conversation not found');
    }

    // Add user message to history
    const userMessageObj = {
      role: 'user',
      content: userMessage,
      timestamp: new Date().toISOString(),
    };
    conversation.history.push(userMessageObj);

    try {
      // Generate response using LLaMA
      const llamaResponse = await this.llamaClient.generateConversationalResponse(
        conversation.history,
        this.availableTools
      );

      // Parse potential tool calls
      const parsedResponse = this.llamaClient.parseToolCall(llamaResponse);

      let assistantMessage;
      let searchResults = null;

      if (parsedResponse.toolCall) {
        // Handle tool call
        const toolResult = await this.handleToolCall(
          parsedResponse.toolName,
          parsedResponse.parameters
        );

        if (parsedResponse.toolName === 'search_hotels' && toolResult.success) {
          // Format hotel search results
          const formattedResults = this.llamaClient.formatHotelResults(toolResult.data);
          assistantMessage = {
            role: 'assistant',
            content: formattedResults,
            timestamp: new Date().toISOString(),
            tool_call: {
              name: parsedResponse.toolName,
              parameters: parsedResponse.parameters,
            },
          };
          searchResults = toolResult.data;
          conversation.status = 'search_completed';
        } else if (parsedResponse.toolName === 'validate_booking_parameters') {
          // Handle parameter validation
          const validationResult = toolResult.data;
          if (validationResult.valid) {
            assistantMessage = {
              role: 'assistant',
              content: "Perfect! I have all the information I need. Let me search for hotels for you now...",
              timestamp: new Date().toISOString(),
            };
          } else {
            const missingParams = validationResult.missing_parameters;
            const issues = validationResult.validation_issues;

            let responseText = "I still need a few more details:\n";
            if (missingParams.includes('destination')) {
              responseText += "- Where would you like to stay?\n";
            }
            if (missingParams.includes('checkin_date')) {
              responseText += "- What's your check-in date? (Please use YYYY-MM-DD format)\n";
            }
            if (missingParams.includes('checkout_date')) {
              responseText += "- What's your check-out date? (Please use YYYY-MM-DD format)\n";
            }
            if (missingParams.includes('guests')) {
              responseText += "- How many guests will be staying?\n";
            }

            if (issues.length > 0) {
              responseText += "\nAlso, please note:\n";
              issues.forEach(issue => {
                responseText += `- ${issue}\n`;
              });
            }

            assistantMessage = {
              role: 'assistant',
              content: responseText,
              timestamp: new Date().toISOString(),
            };
          }
        } else {
          // Tool call failed or other tool
          assistantMessage = {
            role: 'assistant',
            content: "I encountered an issue while processing your request. Could you please provide your travel details again?",
            timestamp: new Date().toISOString(),
          };
        }
      } else {
        // Regular conversational response
        assistantMessage = {
          role: 'assistant',
          content: parsedResponse.response,
          timestamp: new Date().toISOString(),
        };
      }

      // Add assistant message to history
      conversation.history.push(assistantMessage);

      // Update booking parameters if we can extract them from the conversation
      this.updateBookingParameters(conversation, userMessage);

      return {
        conversationId,
        message: assistantMessage.content,
        status: conversation.status,
        bookingParameters: conversation.bookingParameters,
        searchResults,
      };

    } catch (error) {
      console.error('Error processing message:', error);

      const errorMessage = {
        role: 'assistant',
        content: "I'm sorry, I encountered an error while processing your request. Could you please try again?",
        timestamp: new Date().toISOString(),
      };

      conversation.history.push(errorMessage);

      return {
        conversationId,
        message: errorMessage.content,
        status: conversation.status,
        error: error.message,
      };
    }
  }

  async handleToolCall(toolName, parameters) {
    try {
      const result = await this.mcpPool.executeToolCall(toolName, parameters);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      console.error(`Tool call error for ${toolName}:`, error);
      return {
        success: false,
        error: error.message,
      };
    }
  }

  updateBookingParameters(conversation, userMessage) {
    const message = userMessage.toLowerCase();

    // Simple parameter extraction (in production, use more sophisticated NLP)

    // Extract dates (YYYY-MM-DD format)
    const dateRegex = /\b(\d{4}-\d{2}-\d{2})\b/g;
    const dates = userMessage.match(dateRegex);
    if (dates && dates.length >= 1) {
      if (!conversation.bookingParameters.checkin_date) {
        conversation.bookingParameters.checkin_date = dates[0];
      } else if (!conversation.bookingParameters.checkout_date && dates.length >= 2) {
        conversation.bookingParameters.checkout_date = dates[1];
      }
    }

    // Extract number of guests
    const guestRegex = /\b(\d+)\s*(?:guest|person|people|pax)\b/i;
    const guestMatch = userMessage.match(guestRegex);
    if (guestMatch && !conversation.bookingParameters.guests) {
      conversation.bookingParameters.guests = parseInt(guestMatch[1]);
    }

    // Extract destination (simple approach - look for city names or "in/to" patterns)
    if (!conversation.bookingParameters.destination) {
      const destinationPatterns = [
        /(?:in|to|at)\s+([A-Za-z\s]+?)(?:\s|$|,|\.|!|\?)/i,
        /^([A-Za-z\s]+?)(?:\s|$|,|\.|!|\?)/i,
      ];

      for (const pattern of destinationPatterns) {
        const match = userMessage.match(pattern);
        if (match && match[1] && match[1].trim().length > 2) {
          const destination = match[1].trim();
          // Filter out common words that aren't destinations
          const excludeWords = ['hotel', 'room', 'night', 'stay', 'book', 'find', 'search', 'need', 'want', 'like', 'would', 'could', 'please'];
          if (!excludeWords.some(word => destination.toLowerCase().includes(word))) {
            conversation.bookingParameters.destination = destination;
            break;
          }
        }
      }
    }
  }

  getConversation(conversationId) {
    return this.conversations.get(conversationId);
  }

  getAllConversations() {
    return Array.from(this.conversations.values());
  }
}
