/**
 * Mock LLaMA client for testing without AWS credentials
 * This simulates the behavior of the real LlamaBedrockClient
 */
export class MockLlamaBedrockClient {
  constructor() {
    this.maxTokens = 2048;
    this.temperature = 0.7;
    this.conversationState = new Map(); // Track conversation state
  }

  async generateResponse(prompt, systemPrompt = null) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
    
    // Simple mock response based on prompt content
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes('destination') || lowerPrompt.includes('where')) {
      return "Great! I'd be happy to help you find a hotel. Where would you like to stay?";
    }
    
    if (lowerPrompt.includes('check-in') || lowerPrompt.includes('date')) {
      return "Perfect! What are your check-in and check-out dates? Please provide them in YYYY-MM-DD format.";
    }
    
    if (lowerPrompt.includes('guest') || lowerPrompt.includes('people')) {
      return "Excellent! How many guests will be staying?";
    }
    
    if (lowerPrompt.includes('search_hotels') || lowerPrompt.includes('TOOL_CALL')) {
      return `TOOL_CALL: search_hotels
PARAMETERS: {"destination": "New York", "checkin_date": "2024-12-15", "checkout_date": "2024-12-18", "guests": 2}`;
    }
    
    return "I understand. Could you please provide more details about your travel plans?";
  }

  async generateConversationalResponse(conversationHistory, availableTools = []) {
    const systemPrompt = this.buildSystemPrompt(availableTools);
    const conversationPrompt = this.buildConversationPrompt(conversationHistory);
    
    // Extract parameters from conversation history
    const parameters = this.extractParametersFromHistory(conversationHistory);
    
    // If we have all parameters, trigger hotel search
    if (parameters.destination && parameters.checkin_date && parameters.checkout_date && parameters.guests) {
      return `TOOL_CALL: search_hotels
PARAMETERS: ${JSON.stringify(parameters)}`;
    }
    
    // Otherwise, ask for missing information
    const missing = this.getMissingParameters(parameters);
    if (missing.length > 0) {
      return this.generateMissingParameterResponse(missing);
    }
    
    return await this.generateResponse(conversationPrompt, systemPrompt);
  }

  extractParametersFromHistory(conversationHistory) {
    const parameters = {
      destination: null,
      checkin_date: null,
      checkout_date: null,
      guests: null
    };
    
    for (const message of conversationHistory) {
      if (message.role === 'user') {
        const content = message.content.toLowerCase();
        
        // Extract destination
        if (!parameters.destination) {
          const destinations = ['new york', 'london', 'paris', 'tokyo', 'dubai', 'singapore', 'sydney', 'barcelona', 'amsterdam', 'berlin'];
          for (const dest of destinations) {
            if (content.includes(dest)) {
              parameters.destination = dest.split(' ').map(word => 
                word.charAt(0).toUpperCase() + word.slice(1)
              ).join(' ');
              break;
            }
          }
        }
        
        // Extract dates
        const dateRegex = /\b(\d{4}-\d{2}-\d{2})\b/g;
        const dates = content.match(dateRegex);
        if (dates) {
          if (!parameters.checkin_date) {
            parameters.checkin_date = dates[0];
          }
          if (!parameters.checkout_date && dates.length > 1) {
            parameters.checkout_date = dates[1];
          }
        }
        
        // Extract guests
        const guestRegex = /\b(\d+)\s*(?:guest|person|people|pax)\b/i;
        const guestMatch = content.match(guestRegex);
        if (guestMatch && !parameters.guests) {
          parameters.guests = parseInt(guestMatch[1]);
        }
      }
    }
    
    return parameters;
  }

  getMissingParameters(parameters) {
    const missing = [];
    if (!parameters.destination) missing.push('destination');
    if (!parameters.checkin_date) missing.push('checkin_date');
    if (!parameters.checkout_date) missing.push('checkout_date');
    if (!parameters.guests) missing.push('guests');
    return missing;
  }

  generateMissingParameterResponse(missing) {
    if (missing.includes('destination')) {
      return "I'd be happy to help you find a hotel! Where would you like to stay?";
    }
    
    if (missing.includes('checkin_date')) {
      return "Great choice of destination! What's your check-in date? Please use YYYY-MM-DD format.";
    }
    
    if (missing.includes('checkout_date')) {
      return "Perfect! And what's your check-out date? Please use YYYY-MM-DD format.";
    }
    
    if (missing.includes('guests')) {
      return "Excellent! How many guests will be staying?";
    }
    
    return "I need a few more details to help you find the perfect hotel.";
  }

  buildSystemPrompt(availableTools = []) {
    let systemPrompt = `You are a helpful hotel search assistant. Your goal is to help users find the perfect hotel by collecting their travel requirements through natural conversation.

REQUIRED INFORMATION TO COLLECT:
1. Destination city or location
2. Check-in date (YYYY-MM-DD format)
3. Check-out date (YYYY-MM-DD format)  
4. Number of guests

CONVERSATION GUIDELINES:
- Be friendly, helpful, and conversational
- Ask for missing information naturally, one piece at a time
- Validate dates (check-in must be in the future, check-out after check-in)
- Confirm all details before searching
- If user provides unclear dates, ask for clarification in YYYY-MM-DD format
- Be flexible with destination names (accept cities, regions, landmarks)

RESPONSE FORMAT:
- Always respond in a conversational, helpful tone
- When you have all required information, use the search_hotels tool
- Format hotel results in a user-friendly way with key details highlighted`;

    if (availableTools.length > 0) {
      systemPrompt += `\n\nAVAILABLE TOOLS:\n`;
      availableTools.forEach(tool => {
        systemPrompt += `- ${tool.name}: ${tool.description}\n`;
      });
      
      systemPrompt += `\nUSE TOOLS WHEN:
- You have all required booking parameters: use search_hotels
- You want to validate collected parameters: use validate_booking_parameters
- Always validate parameters before searching

TOOL USAGE FORMAT:
When calling a tool, format your response as:
TOOL_CALL: tool_name
PARAMETERS: {"param1": "value1", "param2": "value2"}`;
    }

    return systemPrompt;
  }

  buildConversationPrompt(conversationHistory) {
    let prompt = "Here is our conversation so far:\n\n";
    
    conversationHistory.forEach((message, index) => {
      const role = message.role === 'user' ? 'Human' : 'Assistant';
      prompt += `${role}: ${message.content}\n\n`;
    });
    
    prompt += "Please continue the conversation by responding to the human's last message. ";
    prompt += "If you have all required information (destination, check-in date, check-out date, number of guests), ";
    prompt += "use the search_hotels tool to find hotels.";
    
    return prompt;
  }

  parseToolCall(response) {
    // Parse tool calls from LLaMA response
    const toolCallMatch = response.match(/TOOL_CALL:\s*(\w+)/);
    const parametersMatch = response.match(/PARAMETERS:\s*({.*?})/s);
    
    if (toolCallMatch && parametersMatch) {
      try {
        const toolName = toolCallMatch[1];
        const parameters = JSON.parse(parametersMatch[1]);
        
        return {
          toolCall: true,
          toolName,
          parameters,
          originalResponse: response,
        };
      } catch (error) {
        console.error('Error parsing tool call parameters:', error);
        return { toolCall: false, response };
      }
    }
    
    return { toolCall: false, response };
  }

  formatHotelResults(searchResults) {
    if (!searchResults || !searchResults.results || searchResults.results.length === 0) {
      return "I'm sorry, I couldn't find any hotels matching your criteria. Please try adjusting your search parameters.";
    }

    const { search_parameters, results, total_results } = searchResults;
    
    let response = `Great! I found ${total_results} hotels in ${search_parameters.destination} `;
    response += `for ${search_parameters.guests} guest${search_parameters.guests > 1 ? 's' : ''} `;
    response += `from ${search_parameters.checkin_date} to ${search_parameters.checkout_date}:\n\n`;

    results.slice(0, 5).forEach((hotel, index) => {
      response += `${index + 1}. **${hotel.name}** ⭐ ${hotel.rating}/5 (${hotel.review_count} reviews)\n`;
      response += `   📍 ${hotel.location.address} (${hotel.location.distance_to_center} from center)\n`;
      response += `   💰 $${hotel.price_per_night}/night • Total: $${hotel.total_price}\n`;
      response += `   🏨 ${hotel.description}\n`;
      response += `   ✨ Amenities: ${hotel.amenities.slice(0, 4).join(', ')}${hotel.amenities.length > 4 ? '...' : ''}\n`;
      response += `   📋 ${hotel.cancellation_policy}\n\n`;
    });

    if (results.length > 5) {
      response += `... and ${results.length - 5} more hotels available.\n\n`;
    }

    response += "Would you like more details about any of these hotels, or would you like to modify your search criteria?";
    
    return response;
  }
}
