{"name": "llama-search-mcp", "version": "1.0.0", "description": "Conversational hotel search chatbot using LLaMA on Amazon Bedrock with MCP server", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "mcp-server": "node src/mcp-server/index.js", "mcp-http-server": "node src/mcp-server/http-server.js", "test": "jest"}, "keywords": ["llama", "bedrock", "mcp", "hotel-search", "chatbot", "conversational-ai"], "author": "", "license": "MIT", "dependencies": {"@aws-sdk/client-bedrock-runtime": "^3.450.0", "@modelcontextprotocol/sdk": "^0.4.0", "axios": "^1.6.0", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "eventsource": "^2.0.2", "express": "^4.18.2", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}}