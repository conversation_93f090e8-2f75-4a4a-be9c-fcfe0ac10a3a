#!/usr/bin/env node

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

async function testConversationFlow() {
    console.log('🧪 Testing Hotel Search Chatbot Conversation Flow\n');
    
    try {
        // Test 1: Start conversation
        console.log('1. Starting conversation...');
        const startResponse = await axios.post(`${BASE_URL}/conversation/start`);
        
        if (!startResponse.data.success) {
            throw new Error('Failed to start conversation');
        }
        
        const conversationId = startResponse.data.data.conversationId;
        console.log(`✅ Conversation started: ${conversationId}`);
        console.log(`📝 <PERSON><PERSON> says: "${startResponse.data.data.message}"\n`);
        
        // Test 2: Send destination
        console.log('2. Sending destination...');
        const destResponse = await axios.post(`${BASE_URL}/conversation/${conversationId}/message`, {
            message: 'I want to stay in New York'
        });
        
        if (destResponse.data.success) {
            console.log(`✅ Destination sent successfully`);
            console.log(`📝 <PERSON><PERSON> says: "${destResponse.data.data.message}"\n`);
        }
        
        // Test 3: Send dates
        console.log('3. Sending check-in date...');
        const checkinResponse = await axios.post(`${BASE_URL}/conversation/${conversationId}/message`, {
            message: 'Check-in on 2024-12-15'
        });
        
        if (checkinResponse.data.success) {
            console.log(`✅ Check-in date sent successfully`);
            console.log(`📝 Bot says: "${checkinResponse.data.data.message}"\n`);
        }
        
        // Test 4: Send checkout date
        console.log('4. Sending check-out date...');
        const checkoutResponse = await axios.post(`${BASE_URL}/conversation/${conversationId}/message`, {
            message: 'Check-out on 2024-12-18'
        });
        
        if (checkoutResponse.data.success) {
            console.log(`✅ Check-out date sent successfully`);
            console.log(`📝 Bot says: "${checkoutResponse.data.data.message}"\n`);
        }
        
        // Test 5: Send number of guests
        console.log('5. Sending number of guests...');
        const guestsResponse = await axios.post(`${BASE_URL}/conversation/${conversationId}/message`, {
            message: '2 guests'
        });
        
        if (guestsResponse.data.success) {
            console.log(`✅ Number of guests sent successfully`);
            console.log(`📝 Bot says: "${guestsResponse.data.data.message}"\n`);
            
            // Check if we got search results
            if (guestsResponse.data.data.searchResults) {
                console.log('🏨 Hotel search results received!');
                const results = guestsResponse.data.data.searchResults.results;
                console.log(`📊 Found ${results.length} hotels:`);
                
                results.slice(0, 3).forEach((hotel, index) => {
                    console.log(`   ${index + 1}. ${hotel.name} - $${hotel.price_per_night}/night (${hotel.rating}⭐)`);
                });
            }
        }
        
        // Test 6: Get conversation details
        console.log('\n6. Getting conversation details...');
        const detailsResponse = await axios.get(`${BASE_URL}/conversation/${conversationId}`);
        
        if (detailsResponse.data.success) {
            console.log('✅ Conversation details retrieved');
            const details = detailsResponse.data.data;
            console.log(`📋 Status: ${details.status}`);
            console.log(`💬 Messages: ${details.messageCount}`);
            console.log(`📝 Booking Parameters:`, details.bookingParameters);
        }
        
        console.log('\n🎉 All tests passed! The chatbot is working correctly.');
        
    } catch (error) {
        console.error('❌ Test failed:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
        process.exit(1);
    }
}

// Run the test
testConversationFlow();
