# LLaMA Hotel Search Chatbot

A conversational hotel search chatbot powered by Meta's LLaMA model on Amazon Bedrock with MCP (Model Context Protocol) server architecture.

## Features

- 🤖 Natural conversation flow using LLaMA on Amazon Bedrock
- 🏨 Real-time hotel search integration
- 📅 Smart parameter collection (check-in, check-out, guests, destination)
- 🔄 MCP server for API communication
- 💬 Web-based chatbot interface

## Architecture

```
Frontend (Web UI) ↔ Express Server ↔ Amazon Bedrock (LLaMA) ↔ HTTP Connection Pool ↔ MCP HTTP Server ↔ Hotel Search API
```

### Key Components
- **Frontend**: React-like web interface for user interaction
- **Express Server**: Main application server with REST API
- **LLaMA Integration**: Amazon Bedrock for conversational AI (with mock fallback)
- **HTTP Connection Pool**: Manages concurrent MCP connections efficiently
- **MCP HTTP Server**: RESTful server for hotel search operations
- **Hotel Search Service**: Mock hotel data (easily replaceable with real APIs)

## Setup

1. **Clone and install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your AWS credentials and API keys
   ```

3. **Start the application:**
   ```bash
   npm run dev
   ```

   The MCP HTTP server starts automatically! No need to run it separately.

## Configuration

### AWS Bedrock Setup
- Ensure you have AWS credentials configured
- Enable access to LLaMA models in your AWS Bedrock console
- Update the `BEDROCK_MODEL_ID` in your `.env` file

### Hotel Search API
- Configure your preferred hotel search API (Booking.com, Expedia, etc.)
- Update the API credentials in your `.env` file

## Usage

1. Open your browser to `http://localhost:3000`
2. Start a conversation with the chatbot
3. The bot will collect your travel requirements:
   - Destination city
   - Check-in date
   - Check-out date
   - Number of guests
4. Once all parameters are collected, it will search for hotels and display results

## Project Structure

```
src/
├── index.js              # Main Express server
├── bedrock/              # Amazon Bedrock integration
├── mcp-server/           # MCP server implementation
├── conversation/         # Conversation flow logic
├── hotel-search/         # Hotel search API integration
└── public/               # Frontend assets
```

## Development

- `npm run dev` - Start with hot reload (includes MCP HTTP server)
- `npm run mcp-http-server` - Start MCP HTTP server only
- `npm test` - Run tests
- `node test-http-mcp.js` - Test HTTP MCP functionality
- `node test-concurrency.js` - Test concurrent request handling

## Performance

The system handles concurrent requests efficiently:
- ✅ **100% Success Rate** with 10+ concurrent users
- ✅ **Sub-10ms Response Times** for concurrent requests
- ✅ **HTTP Transport** for better scalability and monitoring
- ✅ **Connection Pooling** for optimal resource usage

## Monitoring

- `GET /health` - Application health with MCP pool status
- `GET /api/mcp-pool/status` - Detailed connection pool metrics
- `GET /api/conversations` - List all active conversations
