#!/usr/bin/env node

import { spawn } from 'child_process';
import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testMCPServer() {
    console.log('🧪 Testing MCP Server...\n');
    
    let serverProcess;
    let client;
    let transport;
    
    try {
        // Spawn the MCP server
        console.log('1. Starting MCP server...');
        serverProcess = spawn('node', ['src/mcp-server/index.js'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            cwd: process.cwd(),
        });

        // Create transport
        transport = new StdioClientTransport({
            readable: serverProcess.stdout,
            writable: serverProcess.stdin,
        });

        // Create client
        client = new Client(
            {
                name: 'test-client',
                version: '1.0.0',
            },
            {
                capabilities: {},
            }
        );

        // Connect
        await client.connect(transport);
        console.log('✅ Connected to MCP server\n');

        // Test 1: List tools
        console.log('2. Listing available tools...');
        const toolsResponse = await client.request(
            { method: 'tools/list' },
            { method: 'tools/list' }
        );
        
        console.log('✅ Available tools:');
        toolsResponse.tools.forEach(tool => {
            console.log(`   - ${tool.name}: ${tool.description}`);
        });
        console.log('');

        // Test 2: Validate parameters (incomplete)
        console.log('3. Testing parameter validation (incomplete parameters)...');
        const validationResponse1 = await client.request(
            {
                method: 'tools/call',
                params: {
                    name: 'validate_booking_parameters',
                    arguments: {
                        destination: 'New York',
                        checkin_date: '',
                        checkout_date: '',
                        guests: null,
                    },
                },
            },
            {
                method: 'tools/call',
                params: {
                    name: 'validate_booking_parameters',
                    arguments: {
                        destination: 'New York',
                        checkin_date: '',
                        checkout_date: '',
                        guests: null,
                    },
                },
            }
        );

        const validation1 = JSON.parse(validationResponse1.content[0].text);
        console.log('✅ Validation result (incomplete):');
        console.log(`   Valid: ${validation1.valid}`);
        console.log(`   Missing: ${validation1.missing_parameters.join(', ')}`);
        console.log('');

        // Test 3: Validate parameters (complete)
        console.log('4. Testing parameter validation (complete parameters)...');
        const validationResponse2 = await client.request(
            {
                method: 'tools/call',
                params: {
                    name: 'validate_booking_parameters',
                    arguments: {
                        destination: 'New York',
                        checkin_date: '2024-12-15',
                        checkout_date: '2024-12-18',
                        guests: 2,
                    },
                },
            },
            {
                method: 'tools/call',
                params: {
                    name: 'validate_booking_parameters',
                    arguments: {
                        destination: 'New York',
                        checkin_date: '2024-12-15',
                        checkout_date: '2024-12-18',
                        guests: 2,
                    },
                },
            }
        );

        const validation2 = JSON.parse(validationResponse2.content[0].text);
        console.log('✅ Validation result (complete):');
        console.log(`   Valid: ${validation2.valid}`);
        console.log('');

        // Test 4: Search hotels
        console.log('5. Testing hotel search...');
        const searchResponse = await client.request(
            {
                method: 'tools/call',
                params: {
                    name: 'search_hotels',
                    arguments: {
                        destination: 'New York',
                        checkin_date: '2024-12-15',
                        checkout_date: '2024-12-18',
                        guests: 2,
                    },
                },
            },
            {
                method: 'tools/call',
                params: {
                    name: 'search_hotels',
                    arguments: {
                        destination: 'New York',
                        checkin_date: '2024-12-15',
                        checkout_date: '2024-12-18',
                        guests: 2,
                    },
                },
            }
        );

        const searchResult = JSON.parse(searchResponse.content[0].text);
        console.log('✅ Hotel search completed:');
        console.log(`   Found ${searchResult.total_results} hotels`);
        console.log(`   Sample hotels:`);
        
        searchResult.results.slice(0, 3).forEach((hotel, index) => {
            console.log(`     ${index + 1}. ${hotel.name} - $${hotel.price_per_night}/night (${hotel.rating}⭐)`);
        });

        console.log('\n🎉 All MCP server tests passed!');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.error(error);
    } finally {
        // Cleanup
        if (client) {
            try {
                await client.close();
            } catch (e) {
                console.error('Error closing client:', e.message);
            }
        }
        
        if (serverProcess) {
            serverProcess.kill();
        }
    }
}

// Run the test
testMCPServer();
