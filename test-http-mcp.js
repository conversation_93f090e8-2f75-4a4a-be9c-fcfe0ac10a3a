#!/usr/bin/env node

import { MCPHTTPClient } from './src/conversation/mcp-http-client.js';
import { HotelSearchHTTPServer } from './src/mcp-server/http-server.js';
import axios from 'axios';

async function testHTTPMCPServer() {
  console.log('🧪 Testing HTTP-based MCP Server\n');

  let server = null;
  let client = null;

  try {
    // Start the HTTP server
    console.log('1. Starting MCP HTTP server...');
    server = new HotelSearchHTTPServer(3001);
    await server.start();
    console.log('✅ MCP HTTP server started on port 3001\n');

    // Test direct HTTP endpoints
    console.log('2. Testing direct HTTP endpoints...');

    // Health check
    const healthResponse = await axios.get('http://localhost:3001/health');
    console.log(`✅ Health check: ${healthResponse.data.status}`);

    // List tools
    const toolsResponse = await axios.get('http://localhost:3001/tools');
    console.log(`✅ Available tools: ${toolsResponse.data.tools.length}`);
    toolsResponse.data.tools.forEach(tool => {
      console.log(`   - ${tool.name}: ${tool.description}`);
    });

    // Test parameter validation
    const validationResponse = await axios.post('http://localhost:3001/tools/validate_booking_parameters', {
      destination: 'New York',
      checkin_date: '2025-12-15',
      checkout_date: '2025-12-18',
      guests: 2
    });
    console.log(`✅ Parameter validation: ${validationResponse.data.data.valid ? 'Valid' : 'Invalid'}`);

    // Test hotel search
    const searchResponse = await axios.post('http://localhost:3001/tools/search_hotels', {
      destination: 'New York',
      checkin_date: '2025-12-15',
      checkout_date: '2025-12-18',
      guests: 2
    });
    console.log(`✅ Hotel search: Found ${searchResponse.data.data.total_results} hotels\n`);

    // Test with MCP HTTP Client
    console.log('3. Testing MCP HTTP Client...');
    client = new MCPHTTPClient('http://localhost:3001');
    await client.connect();
    console.log('✅ MCP HTTP client connected');

    // List tools via client
    const clientTools = await client.listTools();
    console.log(`✅ Client tools list: ${clientTools.length} tools`);

    // Validate parameters via client
    const clientValidation = await client.validateBookingParameters({
      destination: 'London',
      checkin_date: '2025-12-20',
      checkout_date: '2025-12-23',
      guests: 3
    });
    console.log(`✅ Client validation: ${clientValidation.valid ? 'Valid' : 'Invalid'}`);

    // Search hotels via client
    const clientSearch = await client.searchHotels({
      destination: 'London',
      checkin_date: '2025-12-20',
      checkout_date: '2025-12-23',
      guests: 3
    });
    console.log(`✅ Client search: Found ${clientSearch.total_results} hotels\n`);

    // Test streaming functionality
    console.log('4. Testing streaming functionality...');
    let progressCount = 0;

    const streamingResult = await client.searchHotelsWithStreaming({
      destination: 'Paris',
      checkin_date: '2025-12-25',
      checkout_date: '2025-12-28',
      guests: 4
    }, (progress) => {
      progressCount++;
      console.log(`   📡 Progress ${progressCount}: ${progress.type} - ${JSON.stringify(progress.data).substring(0, 100)}...`);
    });

    console.log(`✅ Streaming search completed: Found ${streamingResult.total_results} hotels\n`);

    // Test multiple individual operations
    console.log('5. Testing multiple operations...');

    const validation = await client.validateBookingParameters({
      destination: 'Tokyo',
      checkin_date: '2025-12-30',
      checkout_date: '2026-01-02',
      guests: 2
    });

    const search = await client.searchHotels({
      destination: 'Tokyo',
      checkin_date: '2025-12-30',
      checkout_date: '2026-01-02',
      guests: 2
    });

    console.log(`✅ Multiple operations completed:`);
    console.log(`   1. Validation: ${validation.valid ? 'Valid' : 'Invalid'}`);
    console.log(`   2. Search: Found ${search.total_results} hotels`);

    console.log('\n🎉 All HTTP MCP tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error);
  } finally {
    // Cleanup
    if (client) {
      await client.disconnect();
    }

    if (server) {
      await server.stop();
    }
  }
}

async function testConcurrentHTTPRequests() {
  console.log('\n🚀 Testing Concurrent HTTP MCP Requests\n');

  let server = null;

  try {
    // Start server
    server = new HotelSearchHTTPServer(3002);
    await server.start();
    console.log('✅ MCP HTTP server started on port 3002\n');

    // Create multiple clients
    const clients = [];
    for (let i = 0; i < 5; i++) {
      const client = new MCPHTTPClient('http://localhost:3002');
      await client.connect();
      clients.push(client);
    }
    console.log(`✅ Created ${clients.length} concurrent clients\n`);

    // Test concurrent searches
    console.log('Testing concurrent hotel searches...');
    const destinations = ['New York', 'London', 'Paris', 'Tokyo', 'Dubai'];
    const startTime = Date.now();

    const searchPromises = clients.map((client, index) =>
      client.searchHotels({
        destination: destinations[index],
        checkin_date: '2025-12-15',
        checkout_date: '2025-12-18',
        guests: index + 1
      })
    );

    const results = await Promise.allSettled(searchPromises);
    const endTime = Date.now();

    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.length - successful;

    console.log(`📈 Concurrent search results:`);
    console.log(`   Total time: ${endTime - startTime}ms`);
    console.log(`   Successful: ${successful}/${results.length}`);
    console.log(`   Failed: ${failed}/${results.length}`);

    if (successful > 0) {
      const successfulResults = results
        .filter(r => r.status === 'fulfilled')
        .map(r => r.value);

      successfulResults.forEach((result, index) => {
        console.log(`   ${destinations[index]}: ${result.total_results} hotels found`);
      });
    }

    // Cleanup clients
    await Promise.all(clients.map(client => client.disconnect()));

    console.log('\n🎉 Concurrent HTTP MCP tests passed!');

  } catch (error) {
    console.error('❌ Concurrent test failed:', error.message);
  } finally {
    if (server) {
      await server.stop();
    }
  }
}

// Run tests
async function runAllTests() {
  try {
    await testHTTPMCPServer();
    await testConcurrentHTTPRequests();
  } catch (error) {
    console.error('Test suite failed:', error);
    process.exit(1);
  }
}

runAllTests();
