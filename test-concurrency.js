#!/usr/bin/env node

import axios from 'axios';

const BASE_URL = 'http://localhost:3000/api';

async function createConversation(userId) {
  const startTime = Date.now();
  
  try {
    console.log(`[User ${userId}] Starting conversation...`);
    
    // Start conversation
    const startResponse = await axios.post(`${BASE_URL}/conversation/start`);
    const conversationId = startResponse.data.data.conversationId;
    
    console.log(`[User ${userId}] Conversation started: ${conversationId}`);
    
    // Send messages in sequence
    const messages = [
      `I want to stay in ${['New York', 'London', 'Paris', 'Tokyo'][userId % 4]}`,
      `Check-in on 2024-12-${15 + userId}`,
      `Check-out on 2024-12-${18 + userId}`,
      `${2 + userId} guests`
    ];
    
    for (let i = 0; i < messages.length; i++) {
      const messageResponse = await axios.post(`${BASE_URL}/conversation/${conversationId}/message`, {
        message: messages[i]
      });
      
      console.log(`[User ${userId}] Message ${i + 1} sent: "${messages[i]}"`);
      
      if (messageResponse.data.data.searchResults) {
        const results = messageResponse.data.data.searchResults.results;
        console.log(`[User ${userId}] 🏨 Got ${results.length} hotel results!`);
      }
      
      // Small delay between messages
      await new Promise(resolve => setTimeout(resolve, 100));
    }
    
    const endTime = Date.now();
    console.log(`[User ${userId}] ✅ Completed in ${endTime - startTime}ms`);
    
    return {
      userId,
      conversationId,
      duration: endTime - startTime,
      success: true
    };
    
  } catch (error) {
    const endTime = Date.now();
    console.error(`[User ${userId}] ❌ Failed: ${error.message} (${endTime - startTime}ms)`);
    
    return {
      userId,
      duration: endTime - startTime,
      success: false,
      error: error.message
    };
  }
}

async function checkMCPPoolStatus() {
  try {
    const response = await axios.get(`${BASE_URL}/mcp-pool/status`);
    return response.data.data;
  } catch (error) {
    console.error('Failed to get MCP pool status:', error.message);
    return null;
  }
}

async function testConcurrentRequests() {
  console.log('🧪 Testing Concurrent Hotel Search Requests\n');
  
  // Check initial pool status
  console.log('📊 Initial MCP Pool Status:');
  const initialStatus = await checkMCPPoolStatus();
  if (initialStatus) {
    console.log(`   Total Connections: ${initialStatus.totalConnections}`);
    console.log(`   Active Connections: ${initialStatus.activeConnections}`);
    console.log(`   Available Connections: ${initialStatus.availableConnections}`);
    console.log(`   Waiting Requests: ${initialStatus.waitingRequests}`);
    console.log(`   Max Connections: ${initialStatus.maxConnections}\n`);
  }
  
  // Test with different concurrency levels
  const concurrencyLevels = [2, 5, 10];
  
  for (const concurrency of concurrencyLevels) {
    console.log(`🚀 Testing with ${concurrency} concurrent users...\n`);
    
    const startTime = Date.now();
    
    // Create concurrent conversations
    const promises = [];
    for (let i = 0; i < concurrency; i++) {
      promises.push(createConversation(i));
    }
    
    // Wait for all to complete
    const results = await Promise.allSettled(promises);
    const endTime = Date.now();
    
    // Analyze results
    const successful = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
    const failed = results.length - successful;
    const totalDuration = endTime - startTime;
    
    console.log(`\n📈 Results for ${concurrency} concurrent users:`);
    console.log(`   Total Time: ${totalDuration}ms`);
    console.log(`   Successful: ${successful}/${concurrency}`);
    console.log(`   Failed: ${failed}/${concurrency}`);
    
    if (successful > 0) {
      const successfulResults = results
        .filter(r => r.status === 'fulfilled' && r.value.success)
        .map(r => r.value);
      
      const avgDuration = successfulResults.reduce((sum, r) => sum + r.duration, 0) / successful;
      const minDuration = Math.min(...successfulResults.map(r => r.duration));
      const maxDuration = Math.max(...successfulResults.map(r => r.duration));
      
      console.log(`   Average Response Time: ${avgDuration.toFixed(0)}ms`);
      console.log(`   Min Response Time: ${minDuration}ms`);
      console.log(`   Max Response Time: ${maxDuration}ms`);
    }
    
    // Check pool status after test
    console.log('\n📊 MCP Pool Status After Test:');
    const poolStatus = await checkMCPPoolStatus();
    if (poolStatus) {
      console.log(`   Total Connections: ${poolStatus.totalConnections}`);
      console.log(`   Active Connections: ${poolStatus.activeConnections}`);
      console.log(`   Available Connections: ${poolStatus.availableConnections}`);
      console.log(`   Waiting Requests: ${poolStatus.waitingRequests}`);
    }
    
    console.log('\n' + '='.repeat(60) + '\n');
    
    // Wait a bit before next test
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('🎉 Concurrency testing completed!');
}

// Test individual rapid requests
async function testRapidRequests() {
  console.log('⚡ Testing Rapid Sequential Requests...\n');
  
  const startTime = Date.now();
  
  // Create one conversation and send rapid messages
  const startResponse = await axios.post(`${BASE_URL}/conversation/start`);
  const conversationId = startResponse.data.data.conversationId;
  
  console.log(`Conversation started: ${conversationId}`);
  
  // Send 10 rapid messages
  const promises = [];
  for (let i = 0; i < 10; i++) {
    promises.push(
      axios.post(`${BASE_URL}/conversation/${conversationId}/message`, {
        message: `Message ${i + 1}: Testing rapid requests`
      })
    );
  }
  
  const results = await Promise.allSettled(promises);
  const endTime = Date.now();
  
  const successful = results.filter(r => r.status === 'fulfilled').length;
  const failed = results.length - successful;
  
  console.log(`Results:`);
  console.log(`   Total Time: ${endTime - startTime}ms`);
  console.log(`   Successful: ${successful}/10`);
  console.log(`   Failed: ${failed}/10`);
  
  console.log('\n' + '='.repeat(60) + '\n');
}

// Run tests
async function runAllTests() {
  try {
    await testRapidRequests();
    await testConcurrentRequests();
  } catch (error) {
    console.error('Test suite failed:', error);
  }
}

runAllTests();
